<script setup lang="ts">
import { EditorContent } from '@tiptap/vue-3'
import type { mastodon } from '#shared/types'

const emit = defineEmits<{
  (e: 'request-close'): void
}>()

const { account } = useAuth()

const processing = ref(false)
const errorMessage = ref('')
const editedName = ref('')
const content = computed({
  get: () => account.value?.note ?? '',
  set: (value: string) => {
    // This setter will be used by the editor to update content
    if (account.value) {
      account.value.note = value
    }
  }
})

const displayName = computed({
  get: () => (editedName.value || account.value?.displayName) ?? '',
  set: (name: string) => {
    editedName.value = name
  },
})

const { editor } = useConversationEditor({ content })

// Watch for changes in account note and update editor content
watch(() => account.value?.note, (newNote) => {
  if (newNote !== undefined && editor.value && editor.value.getHTML() !== newNote) {
    editor.value.commands.setContent(newNote)
  }
}, { immediate: true })

async function saveProfile() {
  try {
    errorMessage.value = ''
    const note = editor.value?.getHTML() ?? ''
    processing.value = true

    const updatedAccount = await $fetch<mastodon.v1.Account>('/api/accounts/update_credentials', {
      headers: useRequestHeaders(['cookie']),
      method: 'PATCH',
      body: {
        display_name: displayName.value,
        note,
      },
    })

    // Update the account state with the response from the server
    if (updatedAccount) {
      account.value = updatedAccount
    }

    emit('request-close')
  }
  catch (error) {
    console.error('Failed to save profile:', error)
    errorMessage.value = 'Failed to save profile. Please try again.'
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <div v-if="account" class="settings-profile-editor">
    <div class="settings-profile-editor__field">
      <InputBox
        v-model="displayName"
        :label="$t('settings-profile-appearance-display_name')"
      />
    </div>

    <div class="settings-profile-editor__field">
      <label class="settings-profile-editor__label">
        {{ $t('settings-profile-appearance-bio') }}
      </label>
      <EditorContent
        :editor="editor"
        class="settings-profile-editor__editor message-input theme"
      />
    </div>

    <div
      v-if="errorMessage"
      class="settings-profile-editor__error">
      {{ errorMessage }}
    </div>

    <button
      class="settings-profile-editor__button"
      :class="{ 'settings-profile-editor__button--processing': processing }"
      @click="saveProfile">
      <span>{{ $t('action-save') }}</span>
      <SpinnerIcon v-if="processing" />
    </button>
  </div>
</template>

<style lang="scss">
.settings-profile-editor {
  position: relative;
  display: grid;
  gap: var(--padding-base);
  padding: var(--padding-base);

  &__field {
    margin-bottom: var(--padding-small);
  }

  &__label {
    display: block;
    margin-bottom: var(--padding-mini);
    font-weight: 500;
  }

  &__editor {
    .ProseMirror {
      padding: var(--padding-small) var(--padding-base);
      background: var(--color-panel);
      border-radius: var(--corner-radius);
      margin-top: var(--padding-mini);

      &.ProseMirror-focused {
        outline: none;
        box-shadow: 0 0 0 3px var(--color-bg);
      }
    }
  }

  &__error {
    color: var(--color-error, #ff5555);
    font-size: 0.9em;
    margin-top: var(--padding-mini);
  }

  &__button {
    position: relative;
    margin-left: auto;

    &--processing {
      span {
        opacity: 0.2;
      }
    }
  }
}
</style>
